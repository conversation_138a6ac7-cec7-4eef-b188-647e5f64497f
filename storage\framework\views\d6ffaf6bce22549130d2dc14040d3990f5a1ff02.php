<?php $__env->startSection('title', 'แพคเกจ - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Banner Slider -->
<section class="hero-section position-relative <?php echo e($banners->count() === 0 ? 'hero-fallback' : ''); ?>">
    <?php if($banners->count() > 0): ?>
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="5000">
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <div class="banner-slide" style="background-image: url('<?php echo e(asset('storage/' . $banner->image_path)); ?>');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($banners->count() > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="true" aria-label="Slide <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">แพคเกจบริการ</h1>
                    <p class="lead text-white">แพคเกจบริการจัดงานศพที่ครบครันและเหมาะสมกับทุกครอบครัว</p>
                    <?php if($packages->total() > 0): ?>
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-box-open me-2"></i>
                            มีแพคเกจทั้งหมด <?php echo e($packages->total()); ?> รายการ
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">แพคเกจบริการ</h1>
                    <p class="lead">แพคเกจบริการจัดงานศพที่ครบครันและเหมาะสมกับทุกครอบครัว</p>
                    <?php if($packages->total() > 0): ?>
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-box-open me-2"></i>
                            มีแพคเกจทั้งหมด <?php echo e($packages->total()); ?> รายการ
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Packages Section -->
<section class="py-5">
    <div class="container">
        <?php if($packages->count() > 0): ?>
        <div class="row g-4">
            <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card package-card h-100 <?php echo e($package->is_featured ? 'border-warning' : ''); ?>">
                    <?php if($package->is_featured): ?>
                    <div class="card-header bg-warning text-dark text-center fw-bold">
                        <i class="fas fa-star me-2"></i>แพคเกจแนะนำ
                    </div>
                    <?php endif; ?>
                    
                    <?php if($package->image && file_exists(storage_path('app/public/' . $package->image))): ?>
                    <img src="<?php echo e(asset('storage/' . $package->image)); ?>" class="card-img-top" alt="<?php echo e($package->name); ?>" style="height: 200px; object-fit: cover;">
                    <?php else: ?>
                    <img src="<?php echo e(asset('images/placeholder.svg')); ?>" class="card-img-top" alt="ไม่มีรูปภาพ" style="height: 200px; object-fit: cover;">
                    <?php endif; ?>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?php echo e($package->name); ?></h5>
                        <p class="card-text"><?php echo e($package->description); ?></p>

                        <div class="mb-3">
                            <h6>รายการที่รวมอยู่ในแพคเกจ:</h6>
                            <div class="features">
                                <?php echo nl2br(e($package->features)); ?>

                            </div>
                        </div>

                        <div class="mt-auto">
                            <div class="text-center mb-3">
                                <?php if($package->duration): ?>
                                <div class="small text-muted mb-2"><?php echo e($package->duration); ?></div>
                                <?php endif; ?>
                                <small class="text-muted d-block">สอบถามราคาและรายละเอียดได้ที่เจ้าหน้าที่</small>
                            </div>
                            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary w-100">สอบถามแพคเกจ</a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($packages->hasPages()): ?>
        <div class="mt-5">
            <?php echo $__env->make('custom.simple-pagination', ['paginator' => $packages], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-dove fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีแพคเกจ</h3>
            <p class="text-muted">กรุณาติดต่อเราเพื่อสอบถามแพคเกจบริการจัดงานศพ</p>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        <?php endif; ?>
    </div>
</section>



<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/packages.blade.php ENDPATH**/ ?>