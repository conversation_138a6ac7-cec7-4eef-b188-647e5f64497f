<?php $__env->startSection('title', 'จัดการแบนเนอร์'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">จัดการแบนเนอร์</h3>
                    <a href="<?php echo e(route('admin.banners.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> เพิ่มแบนเนอร์ใหม่
                    </a>
                </div>

                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if($banners->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>รูปภาพ</th>
                                        <th>ชื่อแบนเนอร์</th>
                                        <th>คำอธิบาย</th>
                                        <th>แสดงในหน้า</th>
                                        <th>ลำดับ</th>
                                        <th>สถานะ</th>
                                        <th>วันที่สร้าง</th>
                                        <th>จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($banner->image_path): ?>
                                                <img src="<?php echo e(asset('storage/' . $banner->image_path)); ?>" 
                                                     alt="<?php echo e($banner->title); ?>" 
                                                     class="img-thumbnail" 
                                                     style="width: 80px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                                     style="width: 80px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($banner->title); ?></td>
                                        <td><?php echo e(Str::limit($banner->description, 50)); ?></td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e($banner->display_pages_name); ?></span>
                                        </td>
                                        <td><?php echo e($banner->sort_order); ?></td>
                                        <td>
                                            <?php if($banner->is_active): ?>
                                                <span class="badge bg-success">เปิดใช้งาน</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">ปิดใช้งาน</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($banner->created_at->format('d/m/Y H:i')); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.banners.edit', $banner)); ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.banners.destroy', $banner)); ?>" 
                                                      method="POST" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบแบนเนอร์นี้?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            <?php echo e($banners->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">ยังไม่มีแบนเนอร์</h5>
                            <p class="text-muted">เริ่มต้นโดยการเพิ่มแบนเนอร์แรกของคุณ</p>
                            <a href="<?php echo e(route('admin.banners.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> เพิ่มแบนเนอร์ใหม่
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/admin/banners/index.blade.php ENDPATH**/ ?>