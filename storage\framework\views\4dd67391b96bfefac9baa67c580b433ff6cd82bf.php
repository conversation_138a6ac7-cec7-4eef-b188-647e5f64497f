<?php $__env->startSection('title', 'ข้อความติดต่อ - ระบบจัดการ'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item active">ข้อความติดต่อ</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">ข้อความติดต่อ</h1>
    <div class="d-flex gap-2">
        <span class="badge bg-warning text-dark fs-6">
            ข้อความใหม่: <?php echo e($contacts->where('is_read', false)->count()); ?>

        </span>
        <span class="badge bg-info fs-6">
            ทั้งหมด: <?php echo e($contacts->count()); ?>

        </span>
    </div>
</div>

<?php if($contacts->count() > 0): ?>
<!-- Summary Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-envelope fa-2x text-white-50 mb-2"></i>
                    <h6 class="card-title mb-1">ข้อความทั้งหมด</h6>
                    <h3 class="mb-0"><?php echo e($contacts->count()); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-bell fa-2x opacity-75 mb-2"></i>
                    <h6 class="card-title mb-1">ข้อความใหม่</h6>
                    <h3 class="mb-0"><?php echo e($contacts->where('is_read', false)->count()); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-check fa-2x text-white-50 mb-2"></i>
                    <h6 class="card-title mb-1">อ่านแล้ว</h6>
                    <h3 class="mb-0"><?php echo e($contacts->where('is_read', true)->count()); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-calendar-day fa-2x text-white-50 mb-2"></i>
                    <h6 class="card-title mb-1">วันนี้</h6>
                    <h3 class="mb-0"><?php echo e($contacts->where('created_at', '>=', today())->count()); ?></h3>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <?php if($contacts->count() > 0): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>สถานะ</th>
                        <th>ชื่อ</th>
                        <th>อีเมล</th>
                        <th>โทรศัพท์</th>
                        <th>หัวข้อ</th>
                        <th>วันที่</th>
                        <th>การจัดการ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="<?php echo e(!$contact->is_read ? 'table-warning' : ''); ?>">
                        <td>
                            <?php if($contact->is_read): ?>
                            <span class="badge bg-success">อ่านแล้ว</span>
                            <?php else: ?>
                            <span class="badge bg-warning text-dark">ใหม่</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?php echo e($contact->name); ?></strong>
                        </td>
                        <td>
                            <a href="mailto:<?php echo e($contact->email); ?>" class="text-decoration-none">
                                <?php echo e($contact->email); ?>

                            </a>
                        </td>
                        <td>
                            <?php if($contact->phone): ?>
                            <a href="tel:<?php echo e($contact->phone); ?>" class="text-decoration-none">
                                <?php echo e($contact->phone); ?>

                            </a>
                            <?php else: ?>
                            <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php echo e(Str::limit($contact->subject, 30)); ?>

                        </td>
                        <td>
                            <small><?php echo e($contact->created_at->format('d/m/Y H:i')); ?></small>
                            <br>
                            <small class="text-muted"><?php echo e($contact->created_at->diffForHumans()); ?></small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('admin.contacts.show', $contact->id)); ?>" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> ดู
                                </a>
                                <form action="<?php echo e(route('admin.contacts.delete', $contact->id)); ?>" 
                                      method="POST" class="d-inline"
                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบข้อความนี้?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">ยังไม่มีข้อความติดต่อ</h4>
            <p class="text-muted">เมื่อมีลูกค้าติดต่อเข้ามา ข้อความจะแสดงที่นี่</p>
        </div>
        <?php endif; ?>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/admin/contacts/index.blade.php ENDPATH**/ ?>